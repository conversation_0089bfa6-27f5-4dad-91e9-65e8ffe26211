"use client"

import { usePathname, useParams } from "next/navigation"


import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link";
import {Locale} from "@/global";
import {routing} from "@/i18n/routing";

export default function LocaleSwitcher({ className }: { className?: string }) {
  const pathname = usePathname()
  const params = useParams()

  // Get current locale from params (more reliable than parsing pathname)
  const currentLocale = (params?.locale as Locale) || "sr"
  const otherLocale = routing.locales.find((l) => l !== currentLocale) || "en"

  // Remove current locale from pathname to get the path without locale
  const pathWithoutLocale = pathname.replace(`/${currentLocale}`, "") || ""
  const targetPath = `/${otherLocale}${pathWithoutLocale}`

  return (
    <Button asChild variant="outline" className={className}>
      <Link href={targetPath} prefetch>
        {otherLocale.toUpperCase()}
      </Link>
    </Button>
  )
}

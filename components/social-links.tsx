"use client"

import { Facebook, Instagram, Youtube } from 'lucide-react'
import { site } from "@/lib/site"
import { cn } from "@/lib/utils"

type Props = {
  className?: string
  size?: number
}

export default function SocialLinks({ className, size = 18 }: Props) {
  return (
    <div className={cn("flex items-center gap-3", className)}>
      <a
        href={site.socials.instagram}
        target="_blank"
        rel="noreferrer"
        aria-label="Instagram — Promenada Palić"
        className="text-foreground/70 hover:text-[#F17C9A]"
      >
        <Instagram style={{ width: size, height: size }} />
      </a>
      <a
        href={site.socials.facebook}
        target="_blank"
        rel="noreferrer"
        aria-label="Facebook — Promenada Palić"
        className="text-foreground/70 hover:text-[#0FA3B1]"
      >
        <Facebook style={{ width: size, height: size }} />
      </a>
      <a
        href={site.socials.youtube}
        target="_blank"
        rel="noreferrer"
        aria-label="YouTube — Promenada Palić"
        className="text-foreground/70 hover:text-[#6BCBDB]"
      >
        <Youtube style={{ width: size, height: size }} />
      </a>
    </div>
  )
}

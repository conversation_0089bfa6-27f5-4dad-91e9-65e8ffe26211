"use client";

import { useState } from "react";
import Image from "next/image";
import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogClose, DialogTitle,
} from "@/components/ui/dialog";
import { X } from "lucide-react";

interface ImageCarouselProps {
    images: string[];
    title?: string;
    className?: string;
}

export function ImageCarousel({
                                  images,
                                  title = "Image",
                                  className = "w-[92%]"
                              }: ImageCarouselProps) {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const handleImageClick = (imageSrc: string) => {
        setSelectedImage(imageSrc);
        setIsDialogOpen(true);
    };

    const closeDialog = () => {
        setIsDialogOpen(false);
        setSelectedImage(null);
    };

    const handleBackdropClick = (e: React.MouseEvent) => {
        // Close dialog when clicking on backdrop (not the image)
        if (e.target === e.currentTarget) {
            closeDialog();
        }
    };

    if (!images || images.length === 0) {
        return null;
    }

    return (
        <>
            <Carousel className={className}>
                <CarouselContent>
                    {images.map((img: string, i: number) => (
                        <CarouselItem key={i}>
                            <div className="p-1">
                                <Card>
                                    <CardContent className="relative aspect-video p-0">
                                        <Image
                                            src={img}
                                            alt={`${title} - slika ${i + 1}`}
                                            fill
                                            sizes="(min-width: 1024px) 50vw, 100vw"
                                            className="object-cover rounded-lg cursor-pointer
                               hover:opacity-90 transition-opacity"
                                            onClick={() => handleImageClick(img)}
                                        />
                                    </CardContent>
                                </Card>
                            </div>
                        </CarouselItem>
                    ))}
                </CarouselContent>
                <CarouselPrevious className="hidden md:flex" />
                <CarouselNext className="hidden md:flex" />
            </Carousel>

            {/* Fullscreen Image Dialog */}
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTitle/>
                <DialogContent className="!max-w-none !w-screen !h-screen !p-0
                                 !m-0 !bg-transparent !border-none !rounded-none
                                 fixed !inset-0 !translate-x-0 !translate-y-0
                                 backdrop-blur-sm">
                    <div
                        className="w-full h-full flex items-center justify-center
                       cursor-pointer"
                        onClick={handleBackdropClick}
                    >
                        <DialogClose
                            onClick={closeDialog}
                            className="absolute top-4 right-4 z-50 rounded-full
                         bg-black/50 p-2 text-white hover:bg-black/70
                         transition-colors"
                        >
                            <X className="h-6 w-6" />
                            <span className="sr-only">Close</span>
                        </DialogClose>

                        {selectedImage && (
                            <div className="relative max-w-full max-h-full">
                                <Image
                                    src={selectedImage}
                                    alt="Fullscreen view"
                                    width={0}
                                    height={0}
                                    sizes="100vw"
                                    className="w-auto h-auto max-w-full max-h-full
                           object-contain cursor-default"
                                    priority
                                    onClick={(e) => e.stopPropagation()}
                                />
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}

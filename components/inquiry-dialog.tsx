"use client"

import type * as React from "react"
import { useTransition, useState } from "react"
import { useTranslations } from "next-intl"
import { CalendarIcon, Send } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import { sendInquiry } from "@/app/actions"
import { track } from "@/lib/analytics"

type Props = {
  roomName?: string
  defaultGuests?: number
}

export default function InquiryDialog({ roomName = "Apartman", defaultGuests = 2 }: Props) {
  const t = useTranslations()
  const { toast } = useToast()
  const [open, setOpen] = useState(false)
  const [pending, startTransition] = useTransition()
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({})
  const [guests, setGuests] = useState(defaultGuests)

  async function onSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const name = String(formData.get("name") || "")
    const email = String(formData.get("email") || "")
    const phone = String(formData.get("phone") || "")
    const message = String(formData.get("message") || "")

    startTransition(async () => {
      const res = await sendInquiry({
        room: roomName,
        name,
        email,
        phone,
        guests,
        startDate: dateRange.from?.toISOString() ?? "",
        endDate: dateRange.to?.toISOString() ?? "",
        message,
      })
      track("inquiry_submit", { room_name: roomName, guests })
      if (res?.ok) {
        toast({
          title: t("toast.sent"),
          description: t("toast.desc"),
        })
        setOpen(false)
      } else {
        toast({
          title: "Greška",
          description: "Molimo pokušajte ponovo.",
          variant: "destructive",
        })
      }
    })
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(v) => {
        if (v) track("inquiry_open", { room_name: roomName })
        setOpen(v)
      }}
    >
      <DialogTrigger asChild>
        <Button className="bg-[#0FA3B1] hover:bg-[#0C8995]">{t("nav.inquiry")}</Button>
      </DialogTrigger>
      <DialogContent aria-describedby="upit-opis">
        <DialogHeader>
          <DialogTitle>{t("inquiry.title", { room: roomName })}</DialogTitle>
          <DialogDescription id="upit-opis">{t("inquiry.subtitle")}</DialogDescription>
        </DialogHeader>

        <form onSubmit={onSubmit} className="grid gap-4">
          <div className="grid gap-2">
            <Label htmlFor="name">{t("inquiry.name")}</Label>
            <Input id="name" name="name" placeholder="" required />
          </div>
          <div className="grid gap-2 sm:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="email">{t("inquiry.email")}</Label>
              <Input id="email" type="email" name="email" placeholder="" required />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">{t("inquiry.phone")}</Label>
              <Input id="phone" name="phone" placeholder="" />
            </div>
          </div>

          <div className="grid gap-2 sm:grid-cols-2">
            <div className="grid gap-2">
              <Label>{t("inquiry.dates")}</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className={cn("justify-start text-left font-normal", !dateRange?.from && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange?.from
                      ? dateRange.to
                        ? `${dateRange.from.toLocaleDateString()} — ${dateRange.to.toLocaleDateString()}`
                        : dateRange.from.toLocaleDateString()
                      : t("inquiry.chooseDates")}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    selected={dateRange as any}
                    onSelect={(range: any) => setDateRange(range ?? {})}
                    numberOfMonths={2}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="guests">{t("inquiry.guests")}</Label>
              <Input
                id="guests"
                name="guests"
                type="number"
                min={1}
                max={10}
                value={guests}
                onChange={(e) => setGuests(Number.parseInt(e.target.value || "1", 10))}
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="message">{t("inquiry.message")}</Label>
            <Textarea id="message" name="message" rows={3} />
          </div>

          <DialogFooter>
            <Button type="submit" disabled={pending} className="bg-[#0FA3B1] hover:bg-[#0C8995]">
              <Send className="mr-2 h-4 w-4" />
              {pending ? t("inquiry.sending") : t("inquiry.submit")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

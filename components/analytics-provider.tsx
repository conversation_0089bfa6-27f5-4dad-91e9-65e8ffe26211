"use client"

import { useEffect } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import posthog from "posthog-js"
import Script from "next/script"

const GA_ID = process.env.NEXT_PUBLIC_GA_ID
const PH_KEY = process.env.NEXT_PUBLIC_POSTHOG_KEY
const PH_HOST = process.env.NEXT_PUBLIC_POSTHOG_HOST || "https://us.i.posthog.com"

// Inicijalizacija van komponente da izbegnemo dupli init u StrictMode
if (typeof window !== "undefined" && PH_KEY && !process.env.NODE_ENV?.includes('development')) {
  if (!(window as any).__posthog_inited) {
    posthog.init(PH_KEY, {
      api_host: PH_HOST,
      autocapture: true,
      capture_pageview: false, // mi ručno šaljemo pageview na promenu rute
    })
    ;(window as any).__posthog_inited = true
  }
}

export default function AnalyticsProvider() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Send pageview on route changes
  useEffect(() => {
    if (!GA_ID || !(window as any).gtag) return
    const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : "")
    ;(window as any).gtag("config", GA_ID, {
      page_path: url,
    })
  }, [pathname, searchParams])

  useEffect(() => {
    if (!PH_KEY) return
    const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : "")
    // Ručni pageview da bude tačan za App Router navigacije
    posthog.capture("$pageview", { $current_url: url, path: pathname })
  }, [pathname, searchParams])

  if (!GA_ID && !PH_KEY) return null

  return (
    <>
      {GA_ID && (
        <>
          <Script
            id="gtag-base"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                window.gtag = window.gtag || gtag;
                gtag('js', new Date());
              `,
            }}
          />
          <Script src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`} strategy="afterInteractive" />
          <Script
            id="gtag-config"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `gtag('config', '${GA_ID}', { send_page_view: true });`,
            }}
          />
        </>
      )}
    </>
  )
}

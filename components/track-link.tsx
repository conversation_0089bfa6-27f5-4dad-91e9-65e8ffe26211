"use client"

import * as React from "react"
import { track } from "@/lib/analytics"
import { cn } from "@/lib/utils"

type Props = React.ComponentProps<"a"> & {
  event: string
  params?: Record<string, string | number | boolean | undefined>
}

export default function TrackLink({ event, params, className, onClick, ...rest }: Props) {
  return (
    <a
      {...rest}
      className={cn(className)}
      onClick={(e) => {
        track(event, params)
        onClick?.(e)
      }}
    />
  )
}

import type { ReactNode } from "react"
import type { Metada<PERSON> } from 'next'
import {hasLocale, NextIntlClientProvider} from "next-intl"
import { notFound } from "next/navigation"

import AnalyticsProvider from "@/components/analytics-provider"
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import "../globals.css"
import { Suspense } from "react"
import {routing} from "@/i18n/routing";
import {Locale} from "@/global";

export const metadata: Metadata = {
  title: 'Promenada Palić — smeštaj uz jezero | Apartmani i sobe',
  description: 'Smeštaj na Paliću pored jezera: moderni apartmani i sobe, besplatan Wi‑Fi i parking.',
  generator: 'Next.js',
}

async function getMessages(locale: Locale) {
  try {
    const messages = (await import(`@/messages/${locale}.json`)).default
    return messages
  } catch {
    return null
  }
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export default async function LocaleLayout({
  params,
  children,
}: {
  params: Promise<{ locale: Locale }>
  children: ReactNode
}) {
  const { locale } = await params
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  if (!routing.locales.includes(locale)) notFound()

  const messages = await getMessages(locale)

  return (
    <html lang={locale} suppressHydrationWarning>
      <body>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Suspense fallback={<div>Loading...</div>}>
            {children}
            <AnalyticsProvider />
          </Suspense>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}

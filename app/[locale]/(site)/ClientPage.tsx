"use client";

import Image from "next/image";

import {
  Map<PERSON>in,
  Star,
  Wifi,
  BedDouble,
  Users,
  ChevronRight,
  Phone,
  Mail,
} from "lucide-react";
import { useTranslations } from "next-intl";

import SocialLinks from "@/components/social-links";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import SiteHeader from "@/components/site-header";
import RoomCard from "@/components/room-card";
import rooms from "@/data/rooms.json";
import { site } from "@/lib/site";
import { Link } from "@/i18n/navigation";

export default function ClientPage() {
  const t = useTranslations();
  const tHome = useTranslations("home");
  const tNav = useTranslations("nav");
  const tFooter = useTranslations("footer");

  return (
    <main>
      <SiteHeader />
      <section className="relative h-[100vh]">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/PXL_20250820_124111586.jpg"
            alt="Zalazak sunca na jezeru Palić sa jedrilicom i molovima"
            fill
            priority
            sizes="100vw"
            className="object-cover object-[50%_70%] brightness-[0.6] saturate-110"
          />

          <div className={"absolute h-full w-full bg-black opacity-20"}></div>
        </div>

        <div className="mx-auto max-w-6xl px-4 py-16 text-white sm:py-20 lg:py-28">
          <div className="my-[30%] max-w-2xl">
            <div className="inline-flex items-center gap-3 rounded-full bg-[#0FA3B1]/90 px-3 py-1 text-sm">
              <Star className="h-4 w-4 text-white" />
              <span>{tHome("rating")}</span>
            </div>
            <h1 className="mt-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {tHome("title", { siteName: site.name })}
            </h1>
            <p className="mt-3 text-white/90">{tHome("subtitle")}</p>
            <div className="mt-6 flex flex-wrap gap-3">
              <Button
                asChild
                size="lg"
                className="bg-[#0FA3B1] hover:bg-[#0C8995]"
              >
                <a href="#sobe">{t("home.viewAccommodation")}</a>
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="border-white/40 bg-white/10 text-white hover:bg-white/20"
              >
                <Link
                  href="/znamenitosti"
                  className="inline-flex items-center gap-2"
                >
                  {t("home.attractionsCta")}{" "}
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="mt-6 flex flex-wrap items-center gap-6 text-white/90">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                <span>{t("home.distance")}</span>
              </div>
              <a
                href={site.phoneHref}
                className="flex items-center gap-2 underline decoration-white/40 underline-offset-4"
              >
                <Phone className="h-5 w-5" /> {site.phone}
              </a>
              <a
                href={site.emailHref}
                className="flex items-center gap-2 underline decoration-white/40 underline-offset-4"
              >
                <Mail className="h-5 w-5" /> {site.email}
              </a>
            </div>
          </div>
        </div>
      </section>

      <section id="sobe" className="mx-auto max-w-6xl px-4 py-10 sm:py-12">
        <div className="mb-6 sm:mb-8">
          <h2 className="text-2xl font-semibold tracking-tight">
            {t("home.roomsTitle")}
          </h2>
          <p className="text-muted-foreground">{t("home.roomsSubtitle")}</p>
        </div>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {(rooms as any[]).map((room) => (
            <RoomCard
              key={room.slug}
              slug={room.slug}
              title={room.title}
              image={room.images?.[0]}
              alt={room.primaryImageAlt}
              price={room.price}
              capacity={room.capacity}
            />
          ))}
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 pb-12">
        <Card>
          <CardHeader>
            <CardTitle>{t("home.whyTitle")}</CardTitle>
          </CardHeader>
          <CardContent className="grid gap-4 sm:grid-cols-3">
            <div className="flex items-start gap-3">
              <Wifi className="mt-0.5 h-5 w-5 text-[#0FA3B1]" />
              <div>
                <div className="font-medium">{t("home.wifiTitle")}</div>
                <div className="text-muted-foreground text-sm">
                  {t("home.wifiDesc")}
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <BedDouble className="mt-0.5 h-5 w-5 text-[#F17C9A]" />
              <div>
                <div className="font-medium">{t("home.comfortTitle")}</div>
                <div className="text-muted-foreground text-sm">
                  {t("home.comfortDesc")}
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Users className="mt-0.5 h-5 w-5 text-[#6BCBDB]" />
              <div>
                <div className="font-medium">{t("home.familyTitle")}</div>
                <div className="text-muted-foreground text-sm">
                  {t("home.familyDesc")}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="justify-between">
            <div className="text-muted-foreground text-sm">
              {t("home.planningText")}
            </div>
            <Button
              asChild
              variant="secondary"
              className="border-[#0FA3B1] text-[#0FA3B1]"
            >
              <Link href="/znamenitosti">{t("home.planningCta")}</Link>
            </Button>
          </CardFooter>
        </Card>
      </section>

      <Separator />
      <footer className="text-muted-foreground mx-auto max-w-6xl px-4 py-8 text-sm">
        <div className="flex flex-col items-start justify-between gap-3 sm:flex-row sm:items-center">
          <div>
            {t("footer.copyright", {
              year: new Date().getFullYear(),
              siteName: site.name,
            })}
          </div>
          <div className="flex flex-wrap gap-4">
            <Link className="underline underline-offset-2" href="/znamenitosti">
              {t("nav.attractions")}
            </Link>
            <Link className="underline underline-offset-2" href="/restorani">
              {t("nav.restaurants")}
            </Link>
            <Link className="underline underline-offset-2" href="/desavanja">
              {t("nav.events")}
            </Link>
            <a
              className="underline underline-offset-2"
              href="https://maps.google.com/?q=Palic"
              target="_blank"
              rel="noreferrer"
            >
              {t("footer.location")}
            </a>
            <SocialLinks />
          </div>
        </div>
      </footer>
    </main>
  );
}

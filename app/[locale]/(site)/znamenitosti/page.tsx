import Image from "next/image"
import Link from "next/link"
import type { Metadata } from "next"
import { MapPin, Landmark, Info } from 'lucide-react'

import attractions from "@/data/attractions.json"
import SiteHeader from "@/components/site-header"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "Znamenitosti Palića i Subotice — vodič, istorijat i mapa",
  description:
    "Istorijat Palića i Subotice, uz vodič kroz vile, štrandove, pozornicu, vodotoranj, sinagogu, katedralu, plavu fontanu, gimnaziju i druge znamenitosti. Svaka ima opis i uputstvo do lokacije.",
  alternates: { canonical: "/znamenitosti" }
}

export default function Page() {
  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Palić jezero u zalasku sunca"
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-12 sm:py-16 text-white">
          <Badge className="bg-[#0FA3B1]/90">Vodič</Badge>
          <h1 className="mt-3 text-3xl font-bold tracking-tight sm:text-4xl">
            Znamenitosti Palića i Subotice
          </h1>
          <p className="mt-2 max-w-2xl text-white/90">
            Istorijat i vodič kroz najvažnije znamenitosti. Klik za navigaciju i detalje.
          </p>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {(attractions as any[]).map((a) => (
            <Card key={a.slug}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{a.title}</CardTitle>
                    <div className="flex items-center gap-1 mt-1">
                      <MapPin className="h-4 w-4 text-[#0FA3B1]" />
                      <span className="text-sm text-muted-foreground">{a.city}</span>
                    </div>
                  </div>
                  <Landmark className="h-5 w-5 text-[#F17C9A]" />
                </div>
              </CardHeader>
              <CardContent className="grid gap-3">
                <p className="text-sm text-muted-foreground">{a.short}</p>
                <Link
                  href={`/znamenitosti/${a.slug}`}
                  className="inline-flex items-center gap-2 text-sm text-[#0FA3B1] hover:underline"
                >
                  <Info className="h-4 w-4" />
                  Detalji i istorijat
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </main>
  )
}

import Image from "next/image"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { ArrowLeft, MapPin, Sparkles } from 'lucide-react'

import attractions from "@/data/attractions.json"
import SiteHeader from "@/components/site-header"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import TrackLink from "@/components/track-link"
import { site } from "@/lib/site"

type Attraction = typeof attractions extends (infer A)[] ? A : never

export function generateStaticParams() {
  return (attractions as any[]).map((a) => ({ slug: a.slug }))
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const a = (attractions as any[]).find((a) => a.slug === slug)
  if (!a) return { title: "Znamenitost" }
  return {
    title: `${a.title} — ${a.city}`,
    description: a.short,
    alternates: { canonical: `/znamenitosti/${a.slug}` }
  }
}

export default async function Page({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const a = (attractions as any[]).find((a) => a.slug === slug)
  if (!a) return <div>Znamenitost nije pronađena</div>

  const gmaps = `https://www.google.com/maps/dir/?api=1&origin=${site.coords.lat},${site.coords.lng}&destination=${a.mapsQuery}`
  const place = `https://www.google.com/maps/search/?api=1&query=${a.mapsQuery}`

  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Palić jezero u zalasku sunca"
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-12 sm:py-16 text-white">
          <div className="flex items-center gap-3 mb-4">
            <Button asChild variant="outline" size="sm" className="bg-white/10 border-white/40 text-white hover:bg-white/20">
              <Link href="/znamenitosti" className="inline-flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" /> Nazad
              </Link>
            </Button>
            <Badge className="bg-[#0FA3B1]/90">{a.city}</Badge>
          </div>
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {a.title}
          </h1>
          <p className="mt-2 max-w-2xl text-white/90">
            {a.short}
          </p>
          <div className="mt-4 flex items-center gap-2 text-white/90">
            <MapPin className="h-5 w-5" />
            <span>{a.city}</span>
          </div>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
        <div className="grid gap-6 lg:grid-cols-2">
          <div className="grid gap-4">
            <div className="relative aspect-[16/9] overflow-hidden rounded-lg">
              <Image
                src={a.image || "/placeholder.svg?height=500&width=800&query=znamenitost"}
                alt={a.title}
                fill
                sizes="(min-width: 1024px) 50vw, 100vw"
                className="object-cover"
              />
            </div>
            <article className="prose prose-sm sm:prose-base max-w-none">
              <h2>Istorijat</h2>
              <p>{a.history}</p>
            </article>
            {"story" in a && (a as any).story ? (
              <article className="prose prose-sm sm:prose-base max-w-none">
                <h3 className="inline-flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-[#0FA3B1]" /> Interesantno
                </h3>
                <p>{(a as any).story}</p>
              </article>
            ) : null}
          </div>

          <div className="grid gap-4 h-fit">
            <div className="rounded-lg border p-4">
              <h3 className="font-medium mb-3">Navigacija</h3>
              <div className="grid gap-3">
                <Button asChild>
                  <TrackLink
                    href={gmaps}
                    target="_blank"
                    rel="noreferrer"
                    className="inline-flex items-center gap-2"
                    event="directions_open"
                    params={{ type: "attraction", name: a.title }}
                  >
                    <MapPin className="h-4 w-4" /> Navigacija do lokacije
                  </TrackLink>
                </Button>
                <Button asChild variant="secondary">
                  <TrackLink
                    href={place}
                    target="_blank"
                    rel="noreferrer"
                    className="inline-flex items-center gap-2"
                    event="map_open"
                    params={{ type: "attraction", name: a.title }}
                  >
                    Prikaži na mapi
                  </TrackLink>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

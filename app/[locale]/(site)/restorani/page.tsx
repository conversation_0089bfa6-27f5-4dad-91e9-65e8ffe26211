import Image from "next/image"
import type { Metadata } from "next"
import { ExternalLink, MapPin, Utensils, Clock } from 'lucide-react'

import data from "@/data/restaurants.json"
import SiteHeader from "@/components/site-header"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { site } from "@/lib/site"
import TrackLink from "@/components/track-link"

export const metadata: Metadata = {
  title: "Restorani u blizini — Promenada Palić",
  description:
    "Top izbor: <PERSON>pet<PERSON><PERSON> rit, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, La Biba Ice Cream + subotički: Boss, Basch House, Stara pizzerija, Hausbrandt.",
  alternates: { canonical: "/restorani" }
}

export default function Page() {
  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Palić jezero u zalasku sunca"
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-12 sm:py-16 text-white">
          <Badge className="bg-[#0FA3B1]/90">Preporuke</Badge>
          <h1 className="mt-3 text-3xl font-bold tracking-tight sm:text-4xl">
            Restorani u blizini
          </h1>
          <p className="mt-2 max-w-2xl text-white/90">
            Kratki pregledi popularnih mesta. Klik za navigaciju i prikaz lokacije u Google Maps.
          </p>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
        <div className="grid gap-6 sm:grid-cols-2">
          {(data as any[]).map((r) => {
            const gmaps = `https://www.google.com/maps/dir/?api=1&origin=${site.coords.lat},${site.coords.lng}&destination=${r.mapsQuery}`
            const place = `https://www.google.com/maps/search/?api=1&query=${r.mapsQuery}`
            return (
              <Card key={r.name}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Utensils className="h-5 w-5 text-[#F17C9A]" /> {r.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid gap-3">
                  <p className="text-sm text-muted-foreground">{r.description}</p>
                  {r.menuHint && (
                    <p className="text-xs text-muted-foreground">
                      Meni/ponuda: {r.menuHint}
                    </p>
                  )}
                  {r.hours && (
                    <div className="inline-flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-4 w-4" /> {r.hours}
                    </div>
                  )}
                  <div className="flex flex-wrap gap-3">
                    <Button asChild>
                      <TrackLink
                        href={gmaps}
                        target="_blank"
                        rel="noreferrer"
                        className="inline-flex items-center gap-2"
                        event="directions_open"
                        params={{ type: "restaurant", name: r.name }}
                      >
                        <MapPin className="h-4 w-4" /> Navigacija
                      </TrackLink>
                    </Button>
                    <Button asChild variant="secondary">
                      <TrackLink
                        href={place}
                        target="_blank"
                        rel="noreferrer"
                        className="inline-flex items-center gap-2"
                        event="map_open"
                        params={{ type: "restaurant", name: r.name }}
                      >
                        Lokacija <ExternalLink className="h-3.5 w-3.5" />
                      </TrackLink>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </section>
    </main>
  )
}

"use server"

import { sendOwnerAndGuestEmails } from "@/lib/mailer"

export async function sendInquiry(data: {
  room: string
  name: string
  email: string
  phone?: string
  guests: number
  startDate: string
  endDate: string
  message?: string
}) {
  // Log for dashboards/log drains
  console.log("Primljen upit:", data)

  // Prepare details for email body
  const details =
    `Smeštaj: ${data.room}\n` +
    `Gost: ${data.name}\n` +
    `E-pošta: ${data.email}\n` +
    (data.phone ? `Telefon: ${data.phone}\n` : "") +
    `Gosti: ${data.guests}\n` +
    `Period: ${data.startDate || "-"} do ${data.endDate || "-"}\n` +
    (data.message ? `Poruka: ${data.message}\n` : "")

  // Optional email integration (requires RESEND_API_KEY and RESEND_FROM)
  let emailResult = null
  try {
    console.log("Attempting to send emails for inquiry...")
    emailResult = await sendOwnerAndGuestEmails({
      guestEmail: data.email,
      guestName: data.name,
      roomName: data.room,
      details
    })
    console.log("Email result:", emailResult)
  } catch (e) {
    console.error("Slanje e‑pošte nije uspelo (preskačem):", e)
    emailResult = { sent: false, error: e }
  }

  // Small simulated delay
  await new Promise((r) => setTimeout(r, 600))

  // Return a user-facing automated message
  return {
    ok: true,
    message: "Vaš upit se obrađuje — uskoro ćemo Vas kontaktirati.",
    emailSent: emailResult?.sent || false
  }
}

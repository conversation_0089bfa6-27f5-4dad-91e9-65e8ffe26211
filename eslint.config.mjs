import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
    baseDirectory: __dirname,
});

const eslintConfig = [
    ...compat.extends("next/core-web-vitals", "next/typescript", "prettier"),
    {
        rules: {
            // "@typescript-eslint/no-unused-vars": "warn", // or "off" to completely disable
            // "no-unused-vars": "off", // disable the base rule as it can report incorrect errors
            // "@typescript-eslint/no-explicit-any": "off",
            // "@typescript-eslint/no-unused-expressions": "off",
            // "@typescript-eslint/ban-ts-comment": "off",
            // "prefer-const": "off",
            // "@typescript-eslint/no-wrapper-object-types": "off",
            // "@typescript-eslint/no-require-imports": "off",
        },
    },
];

export default eslintConfig;

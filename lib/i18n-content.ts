import type { Locale } from "@/i18n/config"

// Helper: accept either a string or object like {sr: string, en: string}
export function pickLocale<T extends string | Record<string, string>>(value: T, locale: Locale): string {
  if (value && typeof value === "object" && "sr" in value && "en" in value) {
    const v = (value as Record<string, string>)[locale]
    return v || (value as Record<string, string>)["sr"] || ""
  }
  return String(value ?? "")
}

import { site } from "@/lib/site"

export async function sendOwnerAndGuestEmails(opts: {
  guestEmail: string
  guestName: string
  roomName: string
  details: string
}) {
  const key = process.env.RESEND_API_KEY
  const from = process.env.RESEND_FROM
  const to = process.env.RESEND_TO || site.email

  if (!key || !from) {
    console.warn("Email disabled: missing RESEND_API_KEY or RESEND_FROM")
    return { sent: false, reason: "missing-env" }
  }

  console.log("Sending emails with config:", { from, to, key: key ? "***" : "missing" })

  const { Resend } = await import("resend")
  const resend = new Resend(key)

  const ownerSubject = `Novi upit — ${opts.roomName} — ${opts.guestName}`
  const ownerText = `Stigao je novi upit.\n\n${opts.details}\n\nKontakt gosta: ${opts.guestEmail}\n`
  const guestSubject = `<PERSON><PERSON><PERSON> — potvrda prijema upita`
  const guestText =
    `Poštovani ${opts.guestName},\n\nVaš upit se obrađuje — uskoro ćemo Vas kontaktirati.\n\n` +
    `Hvala na interesovanju za ${opts.roomName} u ${site.name}.\n\nSrdačan pozdrav,\n${site.name}\n${site.phone}\n${site.email}\n`

  try {
    console.log("Attempting to send emails...")

    // For sandbox mode, we'll send both emails to the verified address
    // but include the guest info in the owner email
    const ownerEmailWithGuestInfo = `${ownerText}\n\n--- Guest Information ---\nGuest Email: ${opts.guestEmail}\nGuest Name: ${opts.guestName}\n\nNote: In sandbox mode, this email would normally be sent to the guest.`

    const results = await Promise.all([
      // Send owner notification to verified email
      resend.emails.send({
        from,
        to,
        subject: ownerSubject,
        text: ownerEmailWithGuestInfo
      }),
      // Send guest confirmation to verified email (for testing)
      resend.emails.send({
        from,
        to, // Using verified email instead of opts.guestEmail for sandbox mode
        subject: `[TEST] ${guestSubject} (would be sent to: ${opts.guestEmail})`,
        text: `${guestText}\n\nNote: This is a test email. In production, this would be sent to: ${opts.guestEmail}`
      })
    ])

    console.log("Email send results:", results)
    return { sent: true, results }
  } catch (e) {
    console.error("Resend error:", e)
    // Log more details about the error
    if (e instanceof Error) {
      console.error("Error message:", e.message)
      console.error("Error stack:", e.stack)
    }
    return { sent: false, reason: "resend-error", error: e }
  }
}
